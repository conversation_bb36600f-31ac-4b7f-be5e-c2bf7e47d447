<?php

namespace App\Http\Controllers;

use App\Models\Ticket;
use App\Models\Category;
use App\Models\Label;
use App\Models\User;
use App\Http\Requests\StoreTicketRequest;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class TicketController extends Controller
{
    use AuthorizesRequests;
    public function index()
    {
        // Base query - different for admin vs regular users
        $query = auth()->user()->is_admin
            ? Ticket::query()
            : auth()->user()->tickets();

        // Apply relationships
        $query->with(['user', 'categories', 'labels']);

        // Apply search if provided
        if (request('search')) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . request('search') . '%')
                    ->orWhere('message', 'like', '%' . request('search') . '%');
            });
        }

        // Apply filters only if they exist and user is admin
        if (auth()->user()->is_admin) {
            $filters = request()->only(['status', 'priority', 'category']);

            if (!empty(array_filter($filters))) {
                $query->filter($filters);
            }
        }

        // Get paginated results
        $tickets = $query->latest()->paginate(15);
        $categories = Category::visible()->get();
        $labels = Label::visible()->get();
        return view('tickets.index', compact('tickets', 'categories', 'labels'));
    }

    public function create()
    {
        $categories = Category::visible()->get();
        $labels = Label::visible()->get();

        return view('tickets.create', compact('categories', 'labels'));
    }

    public function store(StoreTicketRequest $request)
    {
        $ticket = auth()->user()->tickets()->create([
            'title' => $request->title,
            'message' => $request->message,
            'priority' => $request->priority,
        ]);

        // Attach categories and labels
        $ticket->categories()->attach($request->categories);
        $ticket->labels()->attach($request->labels);

        return redirect()->route('tickets.show', $ticket)
            ->with('success', 'Ticket created successfully!');
    }

    public function show(Ticket $ticket)
    {
        $this->authorize('view', $ticket);

        $ticket->load(['comments.user', 'categories', 'labels']);

        return view('tickets.show', compact('ticket'));
    }

    public function addComment(Request $request, Ticket $ticket)
    {
        $this->authorize('comment', $ticket);

        $request->validate(['message' => 'required|string']);

        $ticket->comments()->create([
            'user_id' => auth()->id(),
            'message' => $request->message,
        ]);

        return back()->with('success', 'Comment added!');
    }

    public function updateStatus(Request $request, Ticket $ticket)
    {
        $this->authorize('adminAction', Ticket::class);

        $validated = $request->validate([
            'status' => 'required|in:open,in_progress,resolved,closed',
        ]);

        $ticket->update($validated);

        return back()->with('success', 'Ticket status updated!');
    }

    public function assign(Request $request, Ticket $ticket)
    {
        $this->authorize('adminAction', Ticket::class);

        $request->validate([
            'agent_id' => 'required|exists:users,id'
        ]);

        $ticket->update(['assigned_to' => $request->agent_id]);

        return back()->with('success', 'Ticket assigned!');
    }
}
