<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Session\TokenMismatchException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var string[]
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var string[]
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {

        });
    }

    /**
     * Handle unauthenticated requests.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Illuminate\Auth\AuthenticationException $exception
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        // Check if the request expects JSON response (e.g., API request)
        if ($request->expectsJson() || in_array("sanctum", $exception->guards())) {
            return responder()->error(401, "Unauthenticated");
        }

        // If it's a normal web request, redirect to login page
        return redirect()->guest(route('login'));
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Throwable $exception
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Throwable
     */
    public function render($request, Throwable $exception)
    {
        // Handle unauthenticated requests
        if ($exception instanceof AuthenticationException) {
            return $this->unauthenticated($request, $exception);
        }

        // Call parent render method for other exceptions
        return parent::render($request, $exception);
    }

    /**
     * Handle CSRF token mismatch exceptions.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Illuminate\Session\TokenMismatchException $exception
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleTokenMismatch($request, TokenMismatchException $exception)
    {
        // Log the token mismatch for debugging
        \Log::info('CSRF Token Mismatch detected', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip(),
            'user_id' => auth()->id(),
        ]);

        // Handle AJAX requests
        if ($request->expectsJson() || $request->ajax()) {
            return response()->json([
                'message' => 'CSRF token mismatch. Please refresh the page and try again.',
                'error' => 'token_mismatch',
                'redirect' => route('login'),
                'csrf_token' => csrf_token() // Provide fresh token
            ], 419);
        }

        // Handle regular web requests
        // Clear any flash data that might cause issues
        $request->session()->forget(['_flash', '_old_input']);

        // Add a flash message to inform the user
        $request->session()->flash('error', 'Your session has expired. Please log in again.');

        // Redirect to login page
        return redirect()->route('login')->withInput($request->except(['_token', 'password']));
    }
}
