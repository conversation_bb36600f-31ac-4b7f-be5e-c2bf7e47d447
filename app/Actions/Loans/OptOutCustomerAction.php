<?php

namespace App\Actions\Loans;

use App\Models\Customer;
use App\Models\Partner;
use App\Notifications\SmsNotification;
use App\Services\MtnApiService;
use Exception;

class OptOutCustomerAction
{
    /**
     * @throws Exception
     */
    public function execute(Customer $customer, string $partnerCode): bool
    {
        $env = app()->isProduction() ? 'production' : 'test';

        try {
            $api = new MtnApiService($env);
            $result = $api->optOut($customer);

            if (! $result) {
                throw new Exception('Opt-out failed.');
            }

            $options = $customer->options;

            unset($options['savingsaccount']);
            unset($options['loanaccounts']);

            $options['optout_at'] = now()->toDateTimeString();
            $customer->options = $options;
            $customer->save();

            $message = 'You have successfully opted out of the Weekend Loan Service.';
            $partner = Partner::query()->firstWhere('Institution_Code', $partnerCode);

            if ($partner) {
                $customer->notify(new SmsNotification($message, $customer->Telephone_Number, $customer->id, $partner->id));
            }
        } catch (\Throwable $th) {
        }

        return true;
    }
}
