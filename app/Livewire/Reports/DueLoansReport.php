<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetDueLoanReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\DueLoansExport;

class DueLoansReport extends Component
{
    use WithPagination, ExportsData;

    public function mount()
    {
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.due-loans-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.due-loans', [
                'records' => app(GetDueLoanReportDetailsAction::class)
                    ->filters($this->getFilters())
                    ->execute(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters()])
            ->streamFromLivewire();
    }

    public function excelExport()
    {
        return Excel::download(new DueLoansExport($this->getFilters()), $this->getExcelFilename());
    }

    private function getReportData(): \Illuminate\Database\Eloquent\Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Support\Collection
    {
        if (empty($this->endDate)) {
            return collect();
        }

        return app(GetDueLoanReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }
}
