<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetSavingAccountsReportDetailsAction;
use Livewire\Component;

class SavingAccountsReport extends Component
{
    public string $startDate = '';
    public string $endDate = '';

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.saving-accounts-report', [
            'savingAccounts' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        return redirect()->route('export.saving-accounts.pdf', ['startDate' => $this->startDate, 'endDate' => $this->endDate]);
    }

    public function excelExport(): true
    {
        // todo: generate excel
        session()->flash('error', 'Exporting to excel is not yet supported for this report.');

        return true;
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetSavingAccountsReportDetailsAction::class)
            ->paginate()
            ->filters([
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
            ])
            ->execute();
    }
}
