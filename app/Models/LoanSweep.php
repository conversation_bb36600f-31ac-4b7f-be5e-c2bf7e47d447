<?php

namespace App\Models;

use App\Models\Scopes\PartnerScope;
use App\Traits\HasStatuses;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $partner_id
 */
class LoanSweep extends Model
{
    use HasStatuses;

    protected $fillable = [
        'partner_id',
        'loan_id',
        'customer_id',
        'amount',
        'provider_product',
        'provider_transaction_id',
        'loan_product_code',
        'operation_type',
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::addGlobalScope(new PartnerScope);
    }

    public function loan(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Loan::class);
    }
}
