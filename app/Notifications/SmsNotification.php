<?php

namespace App\Notifications;

use App\Models\Transaction;
use App\Traits\NotifyWhen;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SmsNotification extends Notification implements ShouldQueue
{
    use Queueable, NotifyWhen;

    protected $message;
    protected $phoneNumber;
    public $partnerID;
    protected $customerID;
    protected $queuedAt;

    public function __construct($message, $phoneNumber, $customerID, $partnerID)
    {
        $this->message = $message;
        $this->phoneNumber = $phoneNumber;
        $this->partnerID = $partnerID;
        $this->customerID = $customerID;
        $this->queuedAt = now(); // Store the timestamp when notification is created
    }

    public function via(object $notifiable): array
    {
        return ['sms', 'database'];
    }

    public function toDatabase($notifiable): array
    {
        // Check if notification is old when actually being processed
        if ($this->isOld()) {
            Log::info('Old SMS notification skipped for database storage.', [
                'queued_at' => $this->queuedAt,
                'hours_old' => $this->queuedAt->diffInHours(now()),
                'phone_number' => $this->phoneNumber,
                'partner_id' => $this->partnerID,
                'customer_id' => $this->customerID
            ]);
            // Return data indicating this notification was skipped due to age
            return [
                'message' => 'SMS notification skipped - too old (>24 hours)',
                'phoneNumber' => $this->phoneNumber,
                'partnerID' => $this->partnerID,
                'customerID' => $this->customerID,
                'skipped' => true,
                'reason' => 'notification_too_old',
                'original_queued_at' => $this->queuedAt
            ];
        }

        return [
            'message' => $this->message,
            'phoneNumber' => $this->phoneNumber,
            'partnerID' => $this->partnerID,
            'customerID' => $this->customerID
        ];
    }

    public function toSms($notifiable): string
    {
        if ($this->isOld()) {
            // Return special marker to indicate this SMS should not be sent
            return '__SKIP_OLD_NOTIFICATION__';
        }

        return $this->message;
    }

    private function isOld(): bool
    {
        /**
         * Check if this SMS notification has been sitting in the queue for more than 24 hours.
         * We use the timestamp from when the notification was created/queued rather than
         * relying on database notifications which may not exist yet.
         */

        // If queuedAt is not set (shouldn't happen), consider it not old to be safe
        if (!$this->queuedAt) {
            return false;
        }

        // todo: Make the hours configurable
        return $this->queuedAt->diffInHours(now()) >= 24;
    }
}
