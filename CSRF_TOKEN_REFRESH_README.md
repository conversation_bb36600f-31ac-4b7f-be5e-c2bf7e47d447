# CSRF Token Auto-Refresh Implementation

## Overview

This implementation fixes the **419 Page Expired** error on the login page by automatically refreshing CSRF tokens. The solution ensures that users don't encounter token expiration issues while maintaining security.

## Features

- ✅ **Automatic Token Refresh**: Refreshes CSRF tokens every 15 minutes
- ✅ **Pre-submission Refresh**: Refreshes token just before form submission
- ✅ **Fallback Mechanism**: Includes a fallback system if the main script fails
- ✅ **Page Visibility Handling**: Pauses refresh when page is hidden, resumes when visible
- ✅ **Error Handling**: Robust error handling with retry logic
- ✅ **No User Interruption**: Works silently in the background

## Implementation Details

### Files Modified/Created

1. **routes/web.php**
   - Added `GET /refresh-csrf-token` route

2. **app/Http/Controllers/Auth/LoginController.php**
   - Added `refreshCsrfToken()` method

3. **resources/assets/js/csrf-refresh.js**
   - Main CSRF token management class

4. **resources/views/content/authentications/auth-login-basic.blade.php**
   - Added JavaScript includes and fallback script

5. **tests/Feature/CsrfTokenRefreshTest.php**
   - Comprehensive test suite

### How It Works

1. **Main Script (`csrf-refresh.js`)**:
   - Initializes when the login page loads
   - Sets up a 15-minute interval to refresh tokens
   - Refreshes token before form submission
   - Updates all CSRF token inputs and meta tags
   - Handles page visibility changes

2. **Fallback Script (inline)**:
   - Runs if the main script fails to load
   - Provides basic 10-minute refresh interval
   - Ensures basic functionality even if Vite assets fail

3. **Backend Endpoint**:
   - `/refresh-csrf-token` returns fresh CSRF tokens
   - Includes error handling and response validation
   - Returns JSON with token, success status, and timestamp

## Configuration

### Refresh Intervals
- **Main script**: 15 minutes (configurable)
- **Fallback script**: 10 minutes
- **Pre-submission**: Always refreshes before form submit

### Retry Logic
- **Max retries**: 3 attempts
- **Retry delay**: 5 seconds between attempts
- **Error logging**: Console logging for debugging

## Security Considerations

- ✅ Maintains Laravel's CSRF protection
- ✅ Tokens are session-based and secure
- ✅ No sensitive data exposed in JavaScript
- ✅ Endpoint only returns CSRF tokens (no other data)
- ✅ Uses proper AJAX headers for security

## Browser Compatibility

- ✅ Modern browsers (ES6+ features used)
- ✅ Fetch API support required
- ✅ Page Visibility API support (graceful degradation)

## Testing

Run the test suite to verify functionality:

```bash
php artisan test --filter=CsrfTokenRefreshTest
```

### Test Coverage
- ✅ Endpoint returns valid response
- ✅ Token format validation
- ✅ Session consistency
- ✅ AJAX request handling

## Monitoring and Debugging

### Console Logging
The implementation includes comprehensive console logging:

```javascript
// Success messages
"CSRF Token Manager initialized"
"CSRF token refreshed successfully"

// Error messages
"Failed to refresh CSRF token: [error]"
"Max retries reached for CSRF token refresh"

// Fallback messages
"Using fallback CSRF refresh mechanism"
"CSRF token refreshed (fallback)"
```

### Browser Developer Tools
1. Open browser DevTools (F12)
2. Go to Console tab
3. Look for CSRF-related messages
4. Check Network tab for `/refresh-csrf-token` requests

## Troubleshooting

### Common Issues

1. **Script not loading**
   - Check Vite build process
   - Verify file exists in `resources/assets/js/`
   - Fallback script should activate automatically

2. **Token refresh failing**
   - Check Laravel logs for errors
   - Verify route is accessible
   - Check network connectivity

3. **419 errors still occurring**
   - Verify JavaScript is enabled
   - Check browser console for errors
   - Ensure session is not being cleared

### Manual Testing

Test the endpoint manually:
```bash
curl -X GET http://your-domain/refresh-csrf-token \
  -H "Accept: application/json" \
  -H "X-Requested-With: XMLHttpRequest"
```

Expected response:
```json
{
  "csrf_token": "LE46LGg9UcozPVYO3lOUfdunsI6xGP2zPHLdcK9b",
  "success": true,
  "timestamp": "2024-01-01T12:00:00.000000Z"
}
```

## Performance Impact

- **Minimal**: Only runs on login page
- **Efficient**: Uses native Fetch API
- **Smart**: Pauses when page is hidden
- **Lightweight**: ~5KB JavaScript file

## Future Enhancements

Potential improvements for future versions:
- Configurable refresh intervals via admin panel
- Real-time token expiration detection
- Integration with other authentication pages
- Metrics and analytics for token refresh patterns

## Support

For issues or questions:
1. Check browser console for error messages
2. Review Laravel logs for backend errors
3. Run the test suite to verify functionality
4. Check network requests in browser DevTools
