@extends('layouts/contentNavbarLayout')

@section('icon', 'menu-icon tf-icons bx bx-user')
@section('title', 'Tickets')
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Ticket Details</h4>
                        <div
                            class="badge bg-{{ [
                                'low' => 'info',
                                'medium' => 'warning',
                                'high' => 'danger',
                            ][$ticket->priority] }}">
                            {{ ucfirst($ticket->priority) }} Priority
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="ticket-details mb-4">
                            <h3>{{ $ticket->title }}</h3>
                            <p class="text-muted">Created {{ $ticket->created_at->diffForHumans() }} by
                                {{ $ticket->user->name }}</p>

                            <div class="ticket-content py-3">
                                <p>{{ $ticket->message }}</p>
                            </div>

                            @if ($ticket->attachments->count())
                                <div class="attachments mb-4">
                                    <h5>Attachments</h5>
                                    <div class="d-flex flex-wrap gap-2">
                                        @foreach ($ticket->attachments as $attachment)
                                            <div class="border rounded p-2">
                                                <a href="{{ Storage::url($attachment->path) }}" target="_blank">
                                                    <i class="fas fa-paperclip me-1"></i>
                                                    Attachment {{ $loop->iteration }}
                                                </a>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <div class="tags mb-4">
                                <div class="d-flex flex-wrap gap-2">
                                    @foreach ($ticket->categories as $category)
                                        <span class="badge bg-primary">{{ $category->name }}</span>
                                    @endforeach

                                    @foreach ($ticket->labels as $label)
                                        <span class="badge bg-secondary">{{ $label->name }}</span>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="comments-section">
                            <h5>Comments</h5>

                            @foreach ($ticket->comments as $comment)
                                <div class="comment mb-3">
                                    <div class="d-flex justify-content-between">
                                        <strong>{{ $comment->user->name }}</strong>
                                        <small class="text-muted">{{ $comment->created_at->diffForHumans() }}</small>
                                    </div>
                                    <p class="mb-0">{{ $comment->message }}</p>
                                </div>
                            @endforeach

                            <form action="{{ route('tickets.comment', $ticket) }}" method="POST" class="mt-4">
                                @csrf
                                <div class="mb-3">
                                    <label for="comment" class="form-label">Add Comment</label>
                                    <textarea class="form-control" id="comment" name="message" rows="3" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Post Comment</button>
                            </form>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-between">
                        <span
                            class="badge bg-{{ [
                                'open' => 'primary',
                                'in_progress' => 'info',
                                'resolved' => 'success',
                                'closed' => 'secondary',
                            ][$ticket->status] }}">
                            {{ ucfirst(str_replace('_', ' ', $ticket->status)) }}
                        </span>

                        @can('update', $ticket)
                            <div class="btn-group">
                                <a href="{{ route('tickets.edit', $ticket) }}" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>

                                @if (auth()->user()->is_admin)
                                    <form action="{{ route('tickets.update-status', $ticket) }}" method="POST"
                                        class="d-inline">
                                        @csrf
                                        @method('PATCH')
                                        <select name="status" onchange="this.form.submit()" class="form-select form-select-sm">
                                            @foreach (['open', 'in_progress', 'resolved', 'closed'] as $status)
                                                <option value="{{ $status }}"
                                                    {{ $ticket->status === $status ? 'selected' : '' }}>
                                                    {{ ucfirst(str_replace('_', ' ', $status)) }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </form>
                                @endif
                            </div>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
