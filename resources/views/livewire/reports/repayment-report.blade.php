<div class="card">
    <div class="card-header">
        <div class="row">
            <div class="col-md-4">
                <h5 class="mb-0">Repayment Report</h5>
            </div>
            <div class="col-md-8 d-flex justify-content-end">
                <x-date-filter />
                <x-export-buttons :with-excel="true" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <x-session-flash />
        <table id="report-table" class="table table-bordered">
            <thead>
                <tr>
                    <th colspan="5"></th>
                    <th class="text-center" colspan="4">Payments Due</th>
                    <th class="text-center" colspan="4">Payments Made</th>
                    <th></th>
                </tr>
                <tr>
                    <th>Loan #</th>
                    <th class="">Customer</th>
                    <th class="text-end">Phone Number</th>
                    <th class="text-end">Loan Amount</th>
                    <th class="text-end">Last Payment Date</th>
                    <th class="text-end">Principal</th>
                    <th class="text-end">Interest</th>
                    <th class="text-end">Penalty</th>
                    <th class="text-end">Fees</th>
                    <th class="text-end">Principal</th>
                    <th class="text-end">Interest</th>
                    <th class="text-end">Penalty</th>
                    <th class="text-end">Fees</th>
                    <th class="text-end">Total Paid</th>
                </tr>
            </thead>
            <tbody>
                @forelse($records as $record)
                    <tr>
                        <td>{{ $record->id }}</td>
                        <td>{{ $record->customer->name }}</td>
                        <td class="text-end">{{ $record->customer->Telephone_Number }}</td>
                        <td class="text-end"><x-money :value="$record->Credit_Amount" /></td>
                        <td class="text-end">{{ $record->last_payment_date }}</td>
                        <td class="text-end"><x-money :value="$record->principal_due" /></td>
                        <td class="text-end"><x-money :value="$record->interest_due" /></td>
                        <td class="text-end"><x-money :value="$record->penalty_due" /></td>
                        <td class="text-end"><x-money :value="$record->fees_due" /></td>
                        <td class="text-end"><x-money :value="$record->principal_paid" /></td>
                        <td class="text-end"><x-money :value="$record->interest_paid" /></td>
                        <td class="text-end"><x-money :value="$record->penalty_paid" /></td>
                        <td class="text-end"><x-money :value="$record->fees_paid" /></td>
                        <td class="text-end"><x-money :value="$record->total_paid" /></td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="14" class="text-center">No records found</td>
                    </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr>
                    <th class="">Totals</th>
                    <th class="text-end">{{ $records->count() }}</th>
                    <th></th>
                    <th class="text-end"><x-money :value="$records->sum('Credit_Amount')" /></th>
                    <th></th>
                    <th class="text-end"><x-money :value="$records->sum('principal_due')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('interest_due')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('penalty_due')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('fees_due')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('principal_paid')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('interest_paid')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('penalty_paid')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('fees_paid')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('total_paid')" /></th>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
